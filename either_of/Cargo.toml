[package]
name = "either_of"
version = "0.1.6"
authors = ["<PERSON>"]
license = "MIT"
readme = "../README.md"
repository = "https://github.com/leptos-rs/leptos"
description = "Utilities for working with enumerated types that contain one of 2..n other types."
rust-version.workspace = true
edition.workspace = true

[dependencies]
pin-project-lite = { workspace = true, default-features = true }
paste = { workspace = true, default-features = true }

[features]
default = ["no_std"]
no_std = []
