[package]
name = "lazy_routes_e2e"
version = "0.1.0"
edition = "2021"

[dev-dependencies]
anyhow = "1.0"
async-trait = "0.1.81"
cucumber = "0.21.1"
fan<PERSON><PERSON><PERSON> = "0.21.1"
pretty_assertions = "1.4"
serde_json = "1.0"
tokio = { version = "1.39", features = ["macros", "rt-multi-thread", "time"] }
url = "2.5"

[[test]]
name = "app_suite"
harness = false    # Allow Cucumber to print output instead of libtest
