# Leptos Todo App Sqlite with Axum

This example creates a basic todo app with an Axum backend that uses Leptos' server functions to call sqlx from the client and seamlessly run it on the server.

## Getting Started

See the [Examples README](../README.md) for setup and run instructions.

## E2E Testing

See the [E2E README](./e2e/README.md) for more information about the testing strategy.

## Rendering

See the [SSR Notes](../SSR_NOTES.md) for more information about Server Side Rendering.

## Quick Start

Run `cargo leptos watch` to run this example.
