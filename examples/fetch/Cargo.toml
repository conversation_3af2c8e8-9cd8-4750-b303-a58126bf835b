[package]
name = "fetch"
version = "0.1.0"
edition = "2021"

[profile.release]
codegen-units = 1
lto = true

[dependencies]
leptos = { path = "../../leptos", features = ["csr", "tracing"] }
reqwasm = "0.5.0"
gloo-timers = { version = "0.3.0", features = ["futures"] }
serde = { version = "1.0", features = ["derive"] }
log = "0.4.22"
console_log = "1.0"
console_error_panic_hook = "0.1.7"
thiserror = "2.0.12"
tracing = "0.1.40"
tracing-subscriber = "0.3.18"
tracing-subscriber-wasm = "0.1.0"

[dev-dependencies]
wasm-bindgen-test = "0.3.42"
