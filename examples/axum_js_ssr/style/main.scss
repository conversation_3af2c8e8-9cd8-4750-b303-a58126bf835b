html, body {
    margin: 0;
    padding: 0;
    font-family: sans-serif;
    height: 100vh;
    overflow: hidden;
}

body {
    display: flex;
    flex-flow: row nowrap;
}

nav {
    min-width: 17em;
    height: 100vh;
    counter-reset: example-counter 0;
    list-style-type: none;
    list-style-position: outside;
    overflow: auto;
}

nav a {
    display: block;
    padding: 0.5em 2em;
    text-decoration: none;
}

nav a small {
    display: block;
}

nav a.example::before {
    counter-reset: subexample-counter 0;
    counter-increment: example-counter 1;
    content: counter(example-counter) ". ";
}

nav a.subexample::before {
    counter-increment: subexample-counter 1;
    content: counter(example-counter) "." counter(subexample-counter) " ";
}

div#notice {
    display: none;
}

main div#notice.panicked {
    position: sticky;
    top: 0;
    padding: 0.5em 2em;
    display: block;
}

main {
    width: 100%;
    overflow: auto;
}

main article {
    max-width: 60em;
    margin: 0 1em;
    padding: 0 1em;
}

main p, main li {
    line-height: 1.3em;
}

main li pre code, main div pre code {
    display: block;
    line-height: normal;
}

main ol, main ul {
    padding-left: 2em;
}

h2>code, p>code, li>code {
    border-radius: 3px;
    padding: 2px;
}

li pre code, div pre code {
    margin: 0 !important;
    padding: 0 !important;
}

#code-demo {
    overflow-x: auto;
}

#code-demo table {
    width: 50em;
    margin: auto;
}

#code-demo table td {
    vertical-align: top;
}

#code-demo table code {
    display: block;
    padding: 1em;
}

@media (prefers-color-scheme: light) {
    nav {
        background: #f7f7f7;
    }

    nav a {
        color: #000;
    }

    nav a[aria-current="page"] {
        background-color: #e0e0e0;
    }

    nav a:hover, h2>code, p>code, li>code  {
        background-color: #e7e7e7;
    }

    nav a.panicked, main div#notice.panicked {
        background: #fdd;
    }

    main div#notice.panicked a {
        color: #000;
    }

    nav a.section {
        border-bottom: 1px solid #777;
    }
}

@media (prefers-color-scheme: dark) {
    nav {
        background: #080808;
    }

    nav a {
        color: #fff;
    }

    nav a[aria-current="page"] {
        background-color: #3f3f3f;
    }

    nav a:hover, h2>code, p>code, li>code  {
        background-color: #383838;
    }

    nav a.panicked, main div#notice.panicked {
        background: #733;
    }

    main div#notice.panicked a {
        color: #fff;
    }

    nav a.section {
        border-bottom: 1px solid #888;
    }
}

// Just include the raw style as-is because I can't find a quick and easy way to import them just for the
// appropriate media type...
pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}
@media (prefers-color-scheme: light){.hljs{color:#24292e;background:#fff}.hljs-doctag,.hljs-keyword,.hljs-meta .hljs-keyword,.hljs-template-tag,.hljs-template-variable,.hljs-type,.hljs-variable.language_{color:#d73a49}.hljs-title,.hljs-title.class_,.hljs-title.class_.inherited__,.hljs-title.function_{color:#6f42c1}.hljs-attr,.hljs-attribute,.hljs-literal,.hljs-meta,.hljs-number,.hljs-operator,.hljs-selector-attr,.hljs-selector-class,.hljs-selector-id,.hljs-variable{color:#005cc5}.hljs-meta .hljs-string,.hljs-regexp,.hljs-string{color:#032f62}.hljs-built_in,.hljs-symbol{color:#e36209}.hljs-code,.hljs-comment,.hljs-formula{color:#6a737d}.hljs-name,.hljs-quote,.hljs-selector-pseudo,.hljs-selector-tag{color:#22863a}.hljs-subst{color:#24292e}.hljs-section{color:#005cc5;font-weight:700}.hljs-bullet{color:#735c0f}.hljs-emphasis{color:#24292e;font-style:italic}.hljs-strong{color:#24292e;font-weight:700}.hljs-addition{color:#22863a;background-color:#f0fff4}.hljs-deletion{color:#b31d28;background-color:#ffeef0}}
@media (prefers-color-scheme: dark){.hljs{color:#c9d1d9;background:#0d1117}.hljs-doctag,.hljs-keyword,.hljs-meta .hljs-keyword,.hljs-template-tag,.hljs-template-variable,.hljs-type,.hljs-variable.language_{color:#ff7b72}.hljs-title,.hljs-title.class_,.hljs-title.class_.inherited__,.hljs-title.function_{color:#d2a8ff}.hljs-attr,.hljs-attribute,.hljs-literal,.hljs-meta,.hljs-number,.hljs-operator,.hljs-selector-attr,.hljs-selector-class,.hljs-selector-id,.hljs-variable{color:#79c0ff}.hljs-meta .hljs-string,.hljs-regexp,.hljs-string{color:#a5d6ff}.hljs-built_in,.hljs-symbol{color:#ffa657}.hljs-code,.hljs-comment,.hljs-formula{color:#8b949e}.hljs-name,.hljs-quote,.hljs-selector-pseudo,.hljs-selector-tag{color:#7ee787}.hljs-subst{color:#c9d1d9}.hljs-section{color:#1f6feb;font-weight:700}.hljs-bullet{color:#f2cc60}.hljs-emphasis{color:#c9d1d9;font-style:italic}.hljs-strong{color:#c9d1d9;font-weight:700}.hljs-addition{color:#aff5b4;background-color:#033a16}.hljs-deletion{color:#ffdcd7;background-color:#67060c}}
