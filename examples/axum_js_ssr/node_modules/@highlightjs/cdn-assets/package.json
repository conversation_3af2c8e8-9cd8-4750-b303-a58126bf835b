{"name": "@highlightjs/cdn-assets", "description": "Syntax highlighting with language autodetection. (pre-compiled CDN assets)", "keywords": ["highlight", "syntax"], "homepage": "https://highlightjs.org/", "version": "11.10.0", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <****************>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "bugs": {"url": "https://github.com/highlightjs/highlight.js/issues"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git://github.com/highlightjs/highlight.js.git"}, "sideEffects": ["./es/common.js", "./lib/common.js", "*.css", "*.scss"], "scripts": {"mocha": "mocha", "lint": "eslint src/*.js src/lib/*.js demo/*.js tools/**/*.js --ignore-pattern vendor", "lint-languages": "eslint --no-eslintrc -c .eslintrc.lang.js src/languages/**/*.js", "build_and_test": "npm run build && npm run test", "build_and_test_browser": "npm run build-browser && npm run test-browser", "build": "node ./tools/build.js -t node", "build-cdn": "node ./tools/build.js -t cdn", "build-browser": "node ./tools/build.js -t browser :common", "devtool": "npx http-server", "test": "mocha test", "test-markup": "mocha test/markup", "test-detect": "mocha test/detect", "test-browser": "mocha test/browser", "test-parser": "mocha test/parser"}, "engines": {"node": ">=12.0.0"}, "devDependencies": {"@colors/colors": "^1.6.0", "@rollup/plugin-commonjs": "^26.0.1", "@rollup/plugin-json": "^6.0.1", "@rollup/plugin-node-resolve": "^15.2.3", "@types/mocha": "^10.0.2", "@typescript-eslint/eslint-plugin": "^7.15.0", "@typescript-eslint/parser": "^7.15.0", "clean-css": "^5.3.2", "cli-table": "^0.3.1", "commander": "^12.1.0", "css": "^3.0.0", "css-color-names": "^1.0.1", "deep-freeze-es6": "^3.0.2", "del": "^7.1.0", "dependency-resolver": "^2.0.1", "eslint": "^8.57.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "glob": "^8.1.0", "glob-promise": "^6.0.5", "handlebars": "^4.7.8", "http-server": "^14.1.1", "jsdom": "^24.1.0", "lodash": "^4.17.20", "mocha": "^10.2.0", "refa": "^0.4.1", "rollup": "^4.0.2", "should": "^13.2.3", "terser": "^5.21.0", "tiny-worker": "^2.3.0", "typescript": "^5.2.2", "wcag-contrast": "^3.0.0"}}