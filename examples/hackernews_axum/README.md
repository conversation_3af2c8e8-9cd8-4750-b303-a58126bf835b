# Leptos Hacker News Example with Axum

This example creates a basic clone of the Hacker News site. It showcases Leptos' ability to create both a client-side rendered app, and a server side rendered app with hydration, in a single repository. This repo differs from the main Hacker News example by using Axum as it's server.

## Getting Started

See the [Examples README](../README.md) for setup and run instructions.

## Quick Start

Run `trunk serve --open` or `cargo leptos watch` to run this example.
