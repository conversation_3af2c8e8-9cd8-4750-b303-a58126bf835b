[package]
name = "counter_without_macros"
version = "0.1.0"
edition = "2021"
rust-version = "1.75"

[profile.release]
codegen-units = 1
lto = true

[dependencies]
leptos = { path = "../../leptos", features = ["csr"] }
console_error_panic_hook = "0.1.7"

[dev-dependencies]
wasm-bindgen = "0.2"
wasm-bindgen-test = "0.3.42"
pretty_assertions = "1.4"
rstest = "0.22.0"

[dev-dependencies.web-sys]
features = ["HtmlElement", "XPathResult"]
version = "0.3.70"
