[tasks.cargo-all-features]
install_script = '''
cargo install --git https://github.com/sabify/cargo-all-features --branch arbitrary-command-support
'''

[tasks.build]
install_crate = { crate_name = "wasm-pack", binary = "wasm-pack", test_arg = "--help" }
clear = true
command = "deno"
args = ["task", "build"]

[tasks.start-client]
install_crate = { crate_name = "wasm-pack", binary = "wasm-pack", test_arg = "--help" }
command = "deno"
args = ["task", "start"]

[tasks.check]
clear = true
dependencies = ["check-debug", "check-release"]

[tasks.check-debug]
dependencies = ["cargo-all-features"]
command = "cargo"
args = ["all-features", "clippy"]

[tasks.check-release]
dependencies = ["cargo-all-features"]
command = "cargo"
args = ["all-features", "clippy", "--release"]
