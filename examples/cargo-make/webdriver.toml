[tasks.start-webdriver]
script = '''
  BOLD="\e[1m"
  GREEN="\e[0;32m"
  RED="\e[0;31m"
  RESET="\e[0m"

  if command -v chromedriver; then
    if pidof -q chromedriver; then
      echo "  chromedriver is already started"
    else
      echo "Starting chomedriver"
      chromedriver --port=4444 &
    fi
  else
    echo "${RED}${BOLD}ERROR${RESET} - chromedriver not found"
    exit 1
  fi
'''

[tasks.stop-webdriver]
script = '''
  if pidof -q chromedriver; then
    echo "  Stopping chromedriver"
    pkill -ef "chromedriver"
  else
    echo "  chromedriver is already stopped"
  fi
'''

[tasks.webdriver-status]
script = '''
  if pidof -q chromedriver; then
    echo chromedriver is up
  else
    echo chromedriver is not running
  fi
'''
