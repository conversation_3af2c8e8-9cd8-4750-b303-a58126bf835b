[tasks.make-target-site-dir]
command = "mkdir"
args = ["-p", "target/site"]

[tasks.install-cargo-leptos]
install_crate = { crate_name = "cargo-leptos", binary = "cargo-leptos", test_arg = "--help" }

[tasks.cargo-leptos-e2e]
command = "cargo"
args = ["leptos", "end-to-end"]

[tasks.build]
clear = true
command = "cargo"
dependencies = ["make-target-site-dir"]
args = ["leptos", "build", "--release", "-P"]

[tasks.check]
clear = true
dependencies = ["check-debug", "check-release"]

[tasks.check-debug]
dependencies = ["cargo-all-features"]
command = "cargo"
args = ["all-features", "clippy"]

[tasks.check-release]
dependencies = ["cargo-all-features"]
command = "cargo"
args = ["all-features", "clippy", "--release"]

[tasks.start-client]
dependencies = ["install-cargo-leptos"]
command = "cargo"
args = ["leptos", "watch", "--release", "-P"]
