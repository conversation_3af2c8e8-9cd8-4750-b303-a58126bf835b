[tasks.cargo-all-features]
install_script = '''
cargo install --git https://github.com/sabify/cargo-all-features --branch arbitrary-command-support
'''

[tasks.install-cargo-leptos]
install_crate = { crate_name = "cargo-leptos", binary = "cargo-leptos", test_arg = "--help" }
args = ["--locked"]

[tasks.cargo-leptos-e2e]
command = "cargo"
args = ["leptos", "end-to-end"]

[tasks.cargo-leptos-e2e-split]
command = "cargo"
args = ["leptos", "end-to-end", "--split"]

[tasks.build]
clear = true
command = "cargo"
args = ["leptos", "build"]

[tasks.check]
clear = true
dependencies = ["check-debug", "check-release"]

[tasks.check-debug]
dependencies = ["cargo-all-features"]
command = "cargo"
args = ["all-features", "clippy"]

[tasks.check-release]
dependencies = ["cargo-all-features"]
command = "cargo"
args = ["all-features", "clippy", "--release"]

[tasks.start-client]
dependencies = ["install-cargo-leptos"]
command = "cargo"
args = ["leptos", "watch"]
