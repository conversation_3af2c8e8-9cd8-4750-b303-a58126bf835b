[tasks.clean]
dependencies = [
  "clean-cargo",
  "clean-trunk",
  "clean-node_modules",
  "clean-playwright",
  "clean-pkg",
]

[tasks.clean-cargo]
script = '''
find . -type d -name target | xargs rm -rf
'''

[tasks.clean-trunk]
script = '''
find . -type d -name dist | xargs rm -rf
'''

[tasks.clean-node_modules]
script = '''
project_dir=${PWD##*/}
if [ "$project_dir" != "todomvc" ]; then
  find . -type d -name node_modules | xargs rm -rf
fi
'''

[tasks.clean-playwright]
script = '''
find . -name playwright-report -name playwright -name test-results | xargs rm -rf
'''

[tasks.clean-pkg]
script = '''
find . -type d -name pkg | xargs rm -rf
'''
