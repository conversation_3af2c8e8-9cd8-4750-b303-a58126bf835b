# Leptos benchmark example

This example is adoptation of code from [js-framework-benchmark](https://github.com/krausest/js-framework-benchmark/tree/master/frameworks/keyed/leptos).
This example creates a large table with randomized entries, it also shows usage of `template` macro and `For` component.

## Getting Started

See the [Examples README](../README.md) for setup and run instructions.

## Quick Start

Run `trunk serve --open` to run this example.
