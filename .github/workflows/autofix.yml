name: autofix.ci
on:
  pull_request:
  # Running this workflow on main branch pushes requires write permission to apply changes.
  # Leave it alone for future uses.
  # push:
  #   branches: ["main"]
permissions:
  contents: read
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
env:
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: 1
  DEBIAN_FRONTEND: noninteractive
jobs:
  autofix:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions-rust-lang/setup-rust-toolchain@v1
        with: {toolchain: "nightly-2025-07-16", components: "rustfmt, clippy", target: "wasm32-unknown-unknown", rustflags: ""}
      - name: Install Glib
        run: |
          sudo apt-get update
          sudo apt-get install -y libglib2.0-dev
      - name: Install cargo-all-features
        run: cargo install --git https://github.com/sabify/cargo-all-features --branch arbitrary-command-support
      - name: Install jq
        run: sudo apt-get install jq
      - name: Format the workspace
        run: cargo fmt --all
      - name: Clippy the workspace
        run: cargo all-features clippy --allow-dirty --fix --lib --no-deps
      - uses: autofix-ci/action@v1.3.2
        if: ${{ always() }}
        with:
          fail-fast: false
