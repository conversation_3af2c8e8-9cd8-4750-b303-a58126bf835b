name: Examples Changed Call
on:
  workflow_call:
    outputs:
      example_changed:
        description: "Example Changed"
        value: ${{ jobs.get-example-changed.outputs.example_changed }}
      # This is for test-only-examples workflow in ci.yml
      matrix:
        description: "Example Changed Directories"
        value: ${{ jobs.get-example-changed.outputs.matrix }}
jobs:
  get-example-changed:
    name: Get Example Changed
    runs-on: ubuntu-latest
    outputs:
      example_changed: ${{ steps.set-example-changed.outputs.example_changed }}
      # This is for test-only-examples workflow in ci.yml
      matrix: ${{ steps.set-example-changed.outputs.matrix }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Get example files that changed
        id: changed-files
        uses: tj-actions/changed-files@v46
        with:
          files: |
            examples/**
            !examples/cargo-make/**
            !examples/Makefile.toml
            !examples/*.md
      - name: List example files that changed
        run: echo '${{ steps.changed-files.outputs.all_changed_files }}'
      - name: Install jq
        run: sudo apt-get install jq
      - name: Set example_changed
        id: set-example-changed
        run: |
          echo "example_changed=${{ steps.changed-files.outputs.any_changed }}" >> "$GITHUB_OUTPUT"
          # This is for test-only-examples workflow in ci.yml
          echo "matrix={\"directory\": $(echo '${{ steps.changed-files.outputs.all_changed_files }}' | tr ' ' '\n' | awk -F'/' '{print $1 "/" $2}'| sort -u | jq -R -s -c 'split("\n") | .[:-1]')}" >> "$GITHUB_OUTPUT"
