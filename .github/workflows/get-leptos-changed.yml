name: Get Leptos Changed Call
on:
  workflow_call:
    outputs:
      leptos_changed:
        description: "Leptos Changed"
        value: ${{ jobs.create.outputs.leptos_changed }}
jobs:
  create:
    name: Detect Source Change
    runs-on: ubuntu-latest
    outputs:
      leptos_changed: ${{ steps.set-source-changed.outputs.leptos_changed }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Get source files that changed
        id: changed-source
        uses: tj-actions/changed-files@v46
        with:
          files_ignore: |
            .*/**/*
            cargo-make/**/*
            examples/**/*
            projects/**/*
            benchmarks/**/*
            docs/**/*
      - name: List source files that changed
        run: echo '${{ steps.changed-source.outputs.all_changed_files }}'
      - name: Set leptos_changed
        id: set-source-changed
        run: |
          echo "leptos_changed=${{ steps.changed-source.outputs.any_changed }}" >> "$GITHUB_OUTPUT"
