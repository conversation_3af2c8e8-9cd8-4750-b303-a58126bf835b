name: Deploy book
on:
  push:
    paths: ["docs/book/**"]
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest
    permissions:
      contents: write # To push a branch
      pull-requests: write # To create a PR from that branch
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Install mdbook
        run: |
          mkdir mdbook
          curl -sSL https://github.com/rust-lang/mdBook/releases/download/v0.4.27/mdbook-v0.4.27-x86_64-unknown-linux-gnu.tar.gz | tar -xz --directory=./mdbook
          echo `pwd`/mdbook >> $GITHUB_PATH
      - name: Deploy GitHub Pages
        run: |
          cd docs/book
          mdbook build
          git worktree add gh-pages
          git config user.name "Deploy book from CI"
          git config user.email ""
          cd gh-pages
          # Delete the ref to avoid keeping history.
          git update-ref -d refs/heads/gh-pages
          rm -rf *
          mv ../book/* .
          git add .
          git commit -m "Deploy book $GITHUB_SHA to gh-pages"
          git push --force --set-upstream origin gh-pages
