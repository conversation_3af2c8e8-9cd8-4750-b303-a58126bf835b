name: CI
on:
  push:
    branches:
      - main
      - leptos_0.6
      - leptos_0.8
  pull_request:
    branches:
      - main
      - leptos_0.6
      - leptos_0.8
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
jobs:
  get-leptos-changed:
    uses: ./.github/workflows/get-leptos-changed.yml
  get-leptos-matrix:
    uses: ./.github/workflows/get-leptos-matrix.yml
  get-example-changed:
    uses: ./.github/workflows/get-example-changed.yml
  get-examples-matrix:
    uses: ./.github/workflows/get-examples-matrix.yml
  test-members:
    name: CI (members)
    needs: [get-leptos-changed, get-leptos-matrix]
    if: needs.get-leptos-changed.outputs.leptos_changed == 'true'
    strategy:
      matrix: ${{ fromJSON(needs.get-leptos-matrix.outputs.matrix) }}
      fail-fast: false
    uses: ./.github/workflows/run-cargo-make-task.yml
    with:
      directory: ${{ matrix.directory }}
  test-examples:
    name: CI (examples)
    needs: [test-members, get-examples-matrix]
    if: ${{ success() }}
    strategy:
      matrix: ${{ fromJSON(needs.get-examples-matrix.outputs.matrix) }}
      fail-fast: false
    uses: ./.github/workflows/run-cargo-make-task.yml
    with:
      directory: ${{ matrix.directory }}
  test-only-examples:
    name: CI (examples)
    needs: [get-leptos-changed, get-example-changed]
    if: needs.get-leptos-changed.outputs.leptos_changed != 'true' && needs.get-example-changed.outputs.example_changed == 'true'
    strategy:
      matrix: ${{ fromJSON(needs.get-example-changed.outputs.matrix) }}
      fail-fast: false
    uses: ./.github/workflows/run-cargo-make-task.yml
    with:
      directory: ${{ matrix.directory }}
  semver-check:
    name: SemVer check (stable)
    needs: [get-leptos-changed, test-members, test-examples]
    if: ${{ success() && needs.get-leptos-changed.outputs.leptos_changed == 'true' && !contains(github.event.pull_request.labels.*.name, 'breaking') }}
    runs-on: ubuntu-latest
    steps:
      - name: Install Glib
        run: |
          sudo apt-get update
          sudo apt-get install -y libglib2.0-dev
      - name: Checkout
        uses: actions/checkout@v4
      - name: Semver Checks
        uses: obi1kenobi/cargo-semver-checks-action@v2
