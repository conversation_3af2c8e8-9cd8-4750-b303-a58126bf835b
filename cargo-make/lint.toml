[tasks.lint]
dependencies = ["check-format-flow", "clippy-each-feature"]

[tasks.check-format]
env = { LEPTOS_PROJECT_DIRECTORY = "../" }
args = ["fmt", "--", "--check", "--config-path", "${LEPTOS_PROJECT_DIRECTORY}"]

[tasks.clippy-each-feature]
command = "cargo"
args = [
  "all-features",
  "clippy",
  "--no-deps",
  "--",
  "-D",
  "clippy::print_stdout",
]
install_script = '''
cargo install --git https://github.com/sabify/cargo-all-features --branch arbitrary-command-support
'''
